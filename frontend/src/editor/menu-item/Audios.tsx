import AddIcon from "@mui/icons-material/Add";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import ClearIcon from "@mui/icons-material/Clear";
import PauseIcon from "@mui/icons-material/Pause";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import SearchIcon from "@mui/icons-material/Search";
import {
  Alert,
  Box,
  Button,
  Chip,
  CircularProgress,
  IconButton,
  InputAdornment,
  List,
  ListItem,
  Pagination,
  Stack,
  Tab,
  Tabs,
  TextField,
  Typography,
} from "@mui/material";
import React, { useCallback, useRef, useEffect } from "react";
import { AUDIOS } from "../../data/audio";
import {
  JamendoTrack,
  getPopularTracks,
  getTracksByTags,
  searchTracks,
} from "../../services/audioService";
import { StoreContext } from "../../store";
import { getUid } from "../../utils";
import { useLanguage } from "../../i18n/LanguageContext";

// 定义音频源类型
type AudioSource = "local" | "jamendo";

// 定义音乐分类标签
const MUSIC_TAGS = {
  genres: [
    "rock",
    "pop",
    "jazz",
    "electronic",
    "classical",
    "ambient",
    "folk",
    "indie",
  ],
  moods: ["relaxing", "happy", "sad", "energetic", "calm", "epic", "romantic"],
  instruments: ["guitar", "piano", "drums", "violin", "synth", "vocals"],
  usage: [
    "background",
    "soundtrack",
    "advertising",
    "film",
    "games",
    "podcast",
  ],
};

// 所有标签合并成一个数组
const ALL_TAGS = [
  ...MUSIC_TAGS.genres.map((tag) => ({ tag, category: "genres" })),
  ...MUSIC_TAGS.moods.map((tag) => ({ tag, category: "moods" })),
  ...MUSIC_TAGS.instruments.map((tag) => ({ tag, category: "instruments" })),
  ...MUSIC_TAGS.usage.map((tag) => ({ tag, category: "usage" })),
];

// 每页显示的音乐数量
const TRACKS_PER_PAGE = 10;
// 滚动到距离底部多少像素时加载更多
const SCROLL_THRESHOLD = 200;

export const Audios = () => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const [loadingId, setLoadingId] = React.useState<string | null>(null);
  const [playingIndex, setPlayingIndex] = React.useState<number | null>(null);
  const [currentTime, setCurrentTime] = React.useState(0);
  const [duration, setDuration] = React.useState(0);
  const audioRef = React.useRef<HTMLAudioElement>(null);
  const [audioSource, setAudioSource] = React.useState<AudioSource>("local");
  const [jamendoTracks, setJamendoTracks] = React.useState<JamendoTrack[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [selectedTags, setSelectedTags] = React.useState<string[]>([]);
  const [activeCategory, setActiveCategory] = React.useState<
    "genres" | "moods" | "instruments" | "usage"
  >("genres");
  const [jamendoError, setJamendoError] = React.useState<string | null>(null);
  const [playbackError, setPlaybackError] = React.useState<string | null>(null);

  // 分页相关状态
  const [currentPage, setCurrentPage] = React.useState(1);
  const [totalTracks, setTotalTracks] = React.useState(0);
  const [loadingMore, setLoadingMore] = React.useState(false);
  const [hasMoreTracks, setHasMoreTracks] = React.useState(true);

  // 内容区域的引用，用于监听滚动事件
  const contentRef = React.useRef<HTMLDivElement>(null);

  // 标签滚动相关状态
  const tagsContainerRef = React.useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = React.useState(false);
  const [canScrollRight, setCanScrollRight] = React.useState(false);

  React.useEffect(() => {
    if (audioRef.current) {
      audioRef.current.addEventListener("timeupdate", handleTimeUpdate);
      audioRef.current.addEventListener("loadedmetadata", handleLoadedMetadata);
      audioRef.current.addEventListener("ended", () => setPlayingIndex(null));
      audioRef.current.addEventListener("error", handleAudioError);
    }
    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener("timeupdate", handleTimeUpdate);
        audioRef.current.removeEventListener(
          "loadedmetadata",
          handleLoadedMetadata
        );
        audioRef.current.removeEventListener("ended", () =>
          setPlayingIndex(null)
        );
        audioRef.current.removeEventListener("error", handleAudioError);

        // 清理音频播放状态，防止组件卸载后仍有声音
        audioRef.current.pause();
        audioRef.current.src = "";
        audioRef.current.load();
        setPlayingIndex(null);
        setCurrentTime(0);
      }
    };
  }, []);

  // 加载Jamendo音乐
  React.useEffect(() => {
    if (audioSource === "jamendo") {
      setJamendoError(null);
      setCurrentPage(1); // 切换音频源或标签时重置页码
      setHasMoreTracks(true); // 重置是否有更多曲目的状态
      if (selectedTags.length > 0) {
        loadTracksByTags(1);
      } else {
        loadJamendoTracks(1);
      }
    }
  }, [audioSource, selectedTags]);

  // 检查标签滚动状态
  const checkScroll = React.useCallback(() => {
    const container = tagsContainerRef.current;
    if (container) {
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(
        container.scrollLeft < container.scrollWidth - container.clientWidth
      );
    }
  }, []);

  // 监听标签容器滚动和窗口大小变化
  React.useEffect(() => {
    const container = tagsContainerRef.current;
    if (container) {
      checkScroll();
      container.addEventListener("scroll", checkScroll);
      window.addEventListener("resize", checkScroll);
      return () => {
        container.removeEventListener("scroll", checkScroll);
        window.removeEventListener("resize", checkScroll);
      };
    }
  }, [checkScroll, audioSource]);

  // 监听内容区域滚动实现无限滚动
  const handleScroll = useCallback(() => {
    if (
      !contentRef.current ||
      loadingMore ||
      !hasMoreTracks ||
      audioSource !== "jamendo"
    )
      return;

    const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
    // 当滚动到距离底部SCROLL_THRESHOLD像素内时，加载更多
    if (scrollHeight - scrollTop - clientHeight < SCROLL_THRESHOLD) {
      handleLoadMore();
    }
  }, [loadingMore, hasMoreTracks, audioSource, contentRef.current]);

  // 添加滚动事件监听
  useEffect(() => {
    const content = contentRef.current;
    if (content) {
      content.addEventListener("scroll", handleScroll);
      return () => {
        content.removeEventListener("scroll", handleScroll);
      };
    }
  }, [handleScroll]);

  const loadJamendoTracks = async (page: number = currentPage) => {
    if (audioSource === "jamendo") {
      if (page === 1) {
        setIsLoading(true);
      } else {
        setLoadingMore(true);
      }
      setJamendoError(null);

      try {
        const offset = (page - 1) * TRACKS_PER_PAGE;
        const { tracks, total } = await getPopularTracks(
          TRACKS_PER_PAGE,
          offset
        );

        if (page === 1) {
          // 首次加载或重置
          setJamendoTracks(tracks);
        } else {
          // 加载更多
          setJamendoTracks((prev) => [...prev, ...tracks]);
        }

        setTotalTracks(total);
        setCurrentPage(page);

        // 检查是否还有更多数据可加载
        setHasMoreTracks(offset + tracks.length < total && tracks.length > 0);

        if (tracks.length === 0 && page === 1) {
          setJamendoError(t("loading_popular_failed"));
        }
      } catch (error) {
        console.error("加载Jamendo音乐失败:", error);
        setJamendoError(t("loading_jamendo_failed"));
        if (page === 1) setJamendoTracks([]);
        setHasMoreTracks(false);
      } finally {
        setIsLoading(false);
        setLoadingMore(false);
      }
    }
  };

  const loadTracksByTags = async (page: number = currentPage) => {
    if (selectedTags.length === 0) {
      loadJamendoTracks(page);
      return;
    }

    if (page === 1) {
      setIsLoading(true);
    } else {
      setLoadingMore(true);
    }

    setJamendoError(null);
    try {
      const offset = (page - 1) * TRACKS_PER_PAGE;
      const { tracks, total } = await getTracksByTags(
        selectedTags.join("+"),
        TRACKS_PER_PAGE,
        offset
      );

      if (page === 1) {
        // 首次加载或重置
        setJamendoTracks(tracks);
      } else {
        // 加载更多
        setJamendoTracks((prev) => [...prev, ...tracks]);
      }

      setTotalTracks(total);
      setCurrentPage(page);

      // 检查是否还有更多数据可加载
      setHasMoreTracks(offset + tracks.length < total && tracks.length > 0);

      if (tracks.length === 0 && page === 1) {
        setJamendoError(t("no_tag_music_found"));
      }
    } catch (error) {
      console.error("按标签加载音乐失败:", error);
      setJamendoError(t("tag_loading_failed"));
      if (page === 1) setJamendoTracks([]);
      setHasMoreTracks(false);
    } finally {
      setIsLoading(false);
      setLoadingMore(false);
    }
  };

  const handleSearch = async (page: number = 1) => {
    setJamendoError(null);
    if (searchQuery.trim() === "") {
      if (selectedTags.length > 0) {
        loadTracksByTags(page);
      } else {
        loadJamendoTracks(page);
      }
      return;
    }

    if (page === 1) {
      setIsLoading(true);
    } else {
      setLoadingMore(true);
    }

    try {
      const offset = (page - 1) * TRACKS_PER_PAGE;
      const { tracks, total } = await searchTracks(
        searchQuery,
        TRACKS_PER_PAGE,
        offset
      );

      if (page === 1) {
        // 首次搜索或重置
        setJamendoTracks(tracks);
      } else {
        // 加载更多搜索结果
        setJamendoTracks((prev) => [...prev, ...tracks]);
      }

      setTotalTracks(total);
      setCurrentPage(page);
      setSelectedTags([]);

      // 检查是否还有更多数据可加载
      setHasMoreTracks(offset + tracks.length < total && tracks.length > 0);

      if (tracks.length === 0 && page === 1) {
        setJamendoError(t("no_search_result"));
      }
    } catch (error) {
      console.error("搜索Jamendo音乐失败:", error);
      setJamendoError(t("search_failed"));
      if (page === 1) setJamendoTracks([]);
      setHasMoreTracks(false);
    } finally {
      setIsLoading(false);
      setLoadingMore(false);
    }
  };

  const handleLoadMore = () => {
    if (loadingMore || !hasMoreTracks) return;

    setLoadingMore(true);
    const nextPage = currentPage + 1;

    if (searchQuery.trim() !== "") {
      handleSearch(nextPage);
    } else if (selectedTags.length > 0) {
      loadTracksByTags(nextPage);
    } else {
      loadJamendoTracks(nextPage);
    }
  };

  const handleClearSearch = () => {
    setSearchQuery("");
    setJamendoError(null);
    setCurrentPage(1);
    setHasMoreTracks(true);
    if (selectedTags.length > 0) {
      loadTracksByTags(1);
    } else {
      loadJamendoTracks(1);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const handleAudioError = (e: Event) => {
    console.error("音频播放错误:", e);
    setPlaybackError(t("audio_playback_error"));
    setPlayingIndex(null);

    // 3秒后清除错误信息
    setTimeout(() => {
      setPlaybackError(null);
    }, 3000);
  };

  const togglePlay = (index: number, src: string) => {
    if (audioRef.current) {
      setPlaybackError(null); // 清除之前的错误

      if (playingIndex === index) {
        // 暂停当前播放的音频
        audioRef.current.pause();
        audioRef.current.currentTime = 0; // 重置播放位置
        setPlayingIndex(null);
        setCurrentTime(0);
      } else {
        try {
          // 先暂停任何正在播放的音频
          audioRef.current.pause();
          audioRef.current.currentTime = 0;

          // 清理之前的音频源，避免重叠播放
          if (audioRef.current.src !== src) {
            audioRef.current.src = "";
            audioRef.current.load();

            // 设置新的音频源
            audioRef.current.src = src;
            audioRef.current.load();
          }

          // 设置音量，避免爆破音
          audioRef.current.volume = 0.6;

          // 使用 Promise 处理播放请求
          const playPromise = audioRef.current.play();

          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                // 播放成功
                setPlayingIndex(index);
              })
              .catch((error) => {
                console.error("播放失败:", error);
                setPlaybackError(t("audio_playback_error"));
                setPlayingIndex(null);
              });
          }
        } catch (error) {
          console.error("设置音频源失败:", error);
          setPlaybackError(t("audio_playback_error"));
          setPlayingIndex(null);
        }
      }
    }
  };

  const handleAddAudio = (src: string, index: number, audioMetadata?: any) => {
    setLoadingId(index.toString());

    // First add the audio to resources
    const audioIndex = store.addAudioResource(src);
    const id = getUid();

    // Create an audio element
    const audioElement = document.createElement("audio");

    // 处理音频加载失败的情况
    audioElement.onerror = () => {
      console.error("音频加载失败:", src);
      setLoadingId(null);
      setJamendoError(t("audio_loading_failed"));
    };

    audioElement.src = src;
    audioElement.id = `audio-${id}`;
    // Add the element to the document (it can be hidden)
    document.body.appendChild(audioElement);

    // Wait for audio to load before adding to canvas
    audioElement.addEventListener("loadedmetadata", () => {
      store.addAudioElement(audioElement, id, audioMetadata);
      setLoadingId(null);
    });
  };

  const handleSeek = (time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const handleTagClick = (tag: string) => {
    setJamendoError(null);
    setCurrentPage(1); // 重置页码
    setHasMoreTracks(true);
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter((t) => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
    setSearchQuery("");
  };

  // 左右滚动标签
  const handleScrollLeft = () => {
    if (tagsContainerRef.current) {
      tagsContainerRef.current.scrollBy({ left: -200, behavior: "smooth" });
    }
  };

  const handleScrollRight = () => {
    if (tagsContainerRef.current) {
      tagsContainerRef.current.scrollBy({ left: 200, behavior: "smooth" });
    }
  };

  // 添加滚动加载指示器
  const renderLoadingIndicator = () => {
    if (!loadingMore || audioSource !== "jamendo" || !hasMoreTracks)
      return null;

    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          py: 2,
          width: "100%",
          borderTop: "1px solid",
          borderColor: "divider",
        }}
      >
        <CircularProgress size={24} />
      </Box>
    );
  };

  // 结束指示器（当没有更多加载时）
  const renderEndMessage = () => {
    if (
      audioSource !== "jamendo" ||
      hasMoreTracks ||
      jamendoTracks.length === 0
    )
      return null;

    return (
      <Box
        sx={{
          p: 2,
          textAlign: "center",
          borderTop: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="caption" color="text.secondary">
          {t("all_results_displayed").replace(
            "{0}",
            jamendoTracks.length.toString()
          )}
        </Typography>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        width: 250,
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper",
        borderRadius: 2,
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
      }}
    >
      <Box
        sx={{
          bgcolor: "grey.100",
          height: 56,
          display: "flex",
          alignItems: "center",
          px: 3,
          flexShrink: 0,
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "medium" }}>
          {t("audio_library")}
        </Typography>
      </Box>

      <Tabs
        value={audioSource}
        onChange={(_, newValue) => {
          setAudioSource(newValue as AudioSource);
          setJamendoError(null);
          setJamendoTracks([]);
          setSelectedTags([]);
          setSearchQuery("");
          setHasMoreTracks(true);
        }}
        sx={{ borderBottom: 1, borderColor: "divider", bgcolor: "grey.100" }}
      >
        <Tab label={t("local_audio")} value="local" />
        <Tab
          label={
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Box
                component="img"
                src="/assets/icons/Jamendo.png"
                alt="Jamendo"
                sx={{
                  width: 16,
                  height: 16,
                  objectFit: "contain",
                }}
              />
              Jamendo
            </Box>
          }
          value="jamendo"
        />
      </Tabs>

      {audioSource === "jamendo" && (
        <>
          <Box
            sx={{
              p: 2,
              bgcolor: "grey.100",
              borderBottom: 1,
              borderColor: "divider",
            }}
          >
            <TextField
              fullWidth
              size="small"
              placeholder={t("search_placeholder")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === "Enter") {
                  handleSearch();
                }
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchQuery ? (
                  <InputAdornment position="end">
                    <IconButton
                      size="small"
                      onClick={handleClearSearch}
                      edge="end"
                    >
                      <ClearIcon />
                    </IconButton>
                  </InputAdornment>
                ) : null,
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  bgcolor: "background.paper",
                  "&:hover": {
                    "& > fieldset": { borderColor: "primary.main" },
                  },
                },
              }}
            />

            {/* 标签选择区域 - 水平滚动设计 */}
            <Box
              sx={{
                position: "relative",
                mt: 2,
                "&:hover .scroll-button": {
                  opacity: 1,
                },
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  overflowX: "auto",
                  scrollBehavior: "smooth",
                  "&::-webkit-scrollbar": { display: "none" },
                  msOverflowStyle: "none",
                  scrollbarWidth: "none",
                  px: 1,
                  borderRadius: 2,
                }}
                ref={tagsContainerRef}
              >
                <Stack direction="row" spacing={1} sx={{ py: 1 }}>
                  {ALL_TAGS.map(({ tag, category }) => (
                    <Chip
                      key={tag}
                      label={tag}
                      size="small"
                      clickable
                      color={selectedTags.includes(tag) ? "primary" : "default"}
                      onClick={() => handleTagClick(tag)}
                      sx={{ whiteSpace: "nowrap" }}
                    />
                  ))}
                </Stack>
              </Box>

              {/* 左右滚动按钮 */}
              {canScrollLeft && (
                <IconButton
                  className="scroll-button"
                  size="small"
                  onClick={handleScrollLeft}
                  sx={{
                    position: "absolute",
                    left: 0,
                    top: "50%",
                    transform: "translateY(-50%)",
                    bgcolor: "background.paper",
                    boxShadow: 1,
                    opacity: 0.7,
                    "&:hover": { opacity: 1 },
                    zIndex: 2,
                  }}
                >
                  <ChevronLeftIcon fontSize="small" />
                </IconButton>
              )}

              {canScrollRight && (
                <IconButton
                  className="scroll-button"
                  size="small"
                  onClick={handleScrollRight}
                  sx={{
                    position: "absolute",
                    right: 0,
                    top: "50%",
                    transform: "translateY(-50%)",
                    bgcolor: "background.paper",
                    boxShadow: 1,
                    opacity: 0.7,
                    "&:hover": { opacity: 1 },
                    zIndex: 2,
                  }}
                >
                  <ChevronRightIcon fontSize="small" />
                </IconButton>
              )}
            </Box>
          </Box>

          {/* Jamendo 错误信息显示区域 */}
          {jamendoError && audioSource === "jamendo" && !isLoading && (
            <Alert severity="warning" sx={{ m: 1, mb: 0 }}>
              {jamendoError}
            </Alert>
          )}
        </>
      )}

      <Box
        ref={contentRef}
        sx={{
          bgcolor: "grey.100",
          flex: 1,
          overflow: "auto",
          px: 2,
          py: 2,
          "&::-webkit-scrollbar": {
            width: "5px",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(0, 0, 0, 0.2)",
            width: "5px",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.3)",
            },
          },
        }}
      >
        {isLoading ? (
          <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <List>
            {audioSource === "local"
              ? AUDIOS.map((audio, index) => (
                  <AudioItem
                    handleAddAudio={(src) => handleAddAudio(src, index, audio)}
                    audio={audio}
                    loadingId={loadingId}
                    index={index}
                    key={index}
                    isPlaying={playingIndex === index}
                    currentTime={playingIndex === index ? currentTime : 0}
                    duration={playingIndex === index ? duration : 0}
                    onTogglePlay={() => togglePlay(index, audio.src)}
                    onSeek={handleSeek}
                  />
                ))
              : jamendoTracks.length > 0
              ? jamendoTracks.map((track, index) => {
                  const audioData = {
                    name: track.name,
                    author: track.artist_name,
                    src: track.audiodownload,
                    image: track.image,
                    duration: track.duration,
                  };
                  return (
                    <AudioItem
                      handleAddAudio={(src) =>
                        handleAddAudio(src, index, audioData)
                      }
                      audio={audioData}
                      loadingId={loadingId}
                      index={index}
                      key={track.id}
                      isPlaying={playingIndex === index}
                      currentTime={playingIndex === index ? currentTime : 0}
                      duration={playingIndex === index ? duration : 0}
                      onTogglePlay={() =>
                        togglePlay(index, track.audiodownload)
                      }
                      onSeek={handleSeek}
                    />
                  );
                })
              : !jamendoError &&
                audioSource === "jamendo" && (
                  <Box sx={{ p: 2, textAlign: "center" }}>
                    <Typography variant="body2" color="text.secondary">
                      {t("no_music_found")}
                    </Typography>
                  </Box>
                )}
            {/* 本地音频为空的情况 */}
            {audioSource === "local" && AUDIOS.length === 0 && (
              <Box sx={{ p: 2, textAlign: "center" }}>
                <Typography variant="body2" color="text.secondary">
                  {t("no_local_audio")}
                </Typography>
              </Box>
            )}

            {/* 加载中指示器 */}
            {renderLoadingIndicator()}

            {/* 结束信息 */}
            {renderEndMessage()}
          </List>
        )}
      </Box>

      {/* 播放错误提示 */}
      {playbackError && (
        <Alert severity="error" sx={{ m: 1, mb: 0 }}>
          {playbackError}
        </Alert>
      )}

      <audio ref={audioRef} crossOrigin="anonymous" />
    </Box>
  );
};

const AudioItem = ({
  audio,
  handleAddAudio,
  loadingId,
  index,
  isPlaying,
  currentTime,
  duration,
  onTogglePlay,
  onSeek,
}: {
  audio: any;
  handleAddAudio: (src: string) => void;
  loadingId: string | null;
  index: number;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  onTogglePlay: () => void;
  onSeek: (time: number) => void;
}) => {
  const [isHovered, setIsHovered] = React.useState(false);

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const handleProgressClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const progressBar = event.currentTarget;
    const rect = progressBar.getBoundingClientRect();
    const clickPosition = event.clientX - rect.left;
    const percentage = clickPosition / rect.width;
    const newTime = percentage * duration;
    onSeek(newTime);
  };

  // 音乐时长格式化（针对Jamendo的持续时间）
  const formatDuration = (seconds: number) => {
    const min = Math.floor(seconds / 60);
    const sec = Math.floor(seconds % 60);
    return `${min.toString().padStart(2, "0")}:${sec
      .toString()
      .padStart(2, "0")}`;
  };

  // 确定是否显示封面图片
  const hasCoverImage =
    audio.image || (audio.src && audio.src.includes("jamendo"));

  return (
    <ListItem
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      sx={{
        display: "flex",
        flexDirection: "column",
        py: 1,
        mb: 1,
        bgcolor: "background.paper",
        borderRadius: 2,
        boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
        transition: "all 0.2s ease-in-out",
        "&:hover": {
          bgcolor: "action.hover",
          transform: "translateX(4px)",
          boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
        },
      }}
    >
      <Box sx={{ display: "flex", width: "100%", alignItems: "center" }}>
        {/* 封面图片或播放按钮容器 - 改为圆形 */}
        <Box
          sx={{
            position: "relative",
            width: 54,
            height: 54,
            borderRadius: "50%", // 改为圆形
            overflow: "hidden",
            flexShrink: 0,
            bgcolor: "grey.200",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            // 添加碟片外观
            border: "4px solid rgba(0,0,0,0.1)",
            boxShadow: isPlaying ? "0 0 10px rgba(0,0,0,0.3)" : "none",
            transition: "box-shadow 0.3s ease",
          }}
        >
          {/* 封面图片 - 添加旋转动画 */}
          {hasCoverImage && (
            <Box
              component="img"
              src={audio.image}
              alt={audio.name}
              sx={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
                animation: isPlaying ? "spin 4s linear infinite" : "none",
                "@keyframes spin": {
                  "0%": {
                    transform: "rotate(0deg)",
                  },
                  "100%": {
                    transform: "rotate(360deg)",
                  },
                },
                // 当停止播放时平滑停止旋转
                transition: "transform 0.5s ease-out",
              }}
            />
          )}

          {/* 播放/暂停按钮覆盖在图片上 */}
          <Box
            onClick={(e) => {
              e.stopPropagation();
              onTogglePlay();
            }}
            sx={{
              cursor: "pointer",
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              bgcolor:
                isPlaying || isHovered ? "rgba(0,0,0,0.4)" : "transparent",
              transition: "all 0.2s ease-in-out",
              borderRadius: "50%", // 确保覆盖层也是圆形
              "&:hover": {
                bgcolor: "rgba(0,0,0,0.4)",
              },
              zIndex: 3, // 确保此层在最上面，可以接收点击事件
            }}
          >
            {isPlaying ? (
              <PauseIcon sx={{ fontSize: 24, color: "white" }} />
            ) : (
              <PlayArrowIcon
                sx={{
                  fontSize: 24,
                  color: isHovered ? "white" : "transparent",
                }}
              />
            )}
          </Box>

          {/* 中心小圆点，模拟唱片中心孔 */}
          <Box
            sx={{
              position: "absolute",
              width: 8,
              height: 8,
              borderRadius: "50%",
              bgcolor: "background.paper",
              zIndex: 2,
              pointerEvents: "none", // 禁止小圆点接收鼠标事件，让事件透过它传递
            }}
          />
        </Box>

        <Box sx={{ flex: 1, minWidth: 0, ml: 1 }}>
          <Typography
            variant="body2"
            title={audio.name}
            sx={{
              fontWeight: 500,
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {audio.name}
          </Typography>

          <Box sx={{ display: "flex", alignItems: "center" }}>
            {/* 时长 */}
            {audio.duration && (
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ mr: 1 }}
              >
                {formatDuration(audio.duration)}
              </Typography>
            )}

            {/* 作者名称 */}
            {(audio.author || audio.artist_name) && (
              <Typography
                variant="caption"
                color="text.secondary"
                title={audio.author || audio.artist_name}
                sx={{
                  display: "block",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                }}
              >
                {audio.author || audio.artist_name}
              </Typography>
            )}
          </Box>
        </Box>

        {loadingId === index.toString() ? (
          <CircularProgress size={24} sx={{ mr: 1 }} />
        ) : (
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleAddAudio(audio.src);
            }}
            sx={{
              opacity: isHovered ? 1 : 0.4,
              transition: "opacity 0.2s ease-in-out",
              mr: 0,
              backgroundColor: "action.hover",
              "&:hover": {
                bgcolor: "action.selected",
              },
            }}
          >
            <AddIcon fontSize="small" />
          </IconButton>
        )}
      </Box>

      {isPlaying && (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 1,
            width: "100%",
            mt: 1,
            pl: 8,
          }}
        >
          <Box
            onClick={handleProgressClick}
            sx={{
              flex: 1,
              height: 4,
              bgcolor: "action.hover",
              borderRadius: 2,
              position: "relative",
              overflow: "hidden",
              cursor: "pointer",
              "&:hover": {
                height: 6,
                transition: "height 0.2s ease-in-out",
              },
            }}
          >
            <Box
              sx={{
                position: "absolute",
                left: 0,
                top: 0,
                height: "100%",
                width: `${(currentTime / duration) * 100}%`,
                bgcolor: "primary.main",
                borderRadius: 2,
                transition: "width 0.1s linear",
              }}
            />
          </Box>
          <Typography
            variant="caption"
            sx={{
              color: "text.secondary",

              textAlign: "right",
            }}
          >
            {formatTime(currentTime)} | {formatTime(duration)}
          </Typography>
        </Box>
      )}
    </ListItem>
  );
};
