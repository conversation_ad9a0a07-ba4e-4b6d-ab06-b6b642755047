import React, { useCallback, useEffect, useRef } from "react";
import { StoreContext } from "../store";

/**
 * 函数防抖实现
 */
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout;
  return function (...args: any[]) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}

/**
 * 处理GIF静态缩略图的Hook
 * @param element GIF元素
 * @returns 包含缩略图URL和加载状态的对象
 */
export const useGifStaticThumbnail = (element: any) => {
  const [gifStaticThumbnail, setGifStaticThumbnail] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);

  useEffect(() => {
    if (element.type !== "gif" || !element.properties?.src) {
      setGifStaticThumbnail("");
      setIsLoading(false);
      setHasError(false);
      return;
    }

    // 重置状态
    setGifStaticThumbnail("");
    setIsLoading(true);
    setHasError(false);

    // 创建一个临时的img元素来加载GIF
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.src = element.properties.src;

    img.onload = () => {
      // 创建canvas来绘制GIF的第一帧
      const canvas = document.createElement("canvas");
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        setIsLoading(false);
        setHasError(true);
        return;
      }

      // 绘制图片（这会自动显示第一帧）
      ctx.drawImage(img, 0, 0);

      // 转换为静态图片URL
      setGifStaticThumbnail(canvas.toDataURL("image/jpeg", 0.8));
      setIsLoading(false);
    };

    img.onerror = (err) => {
      console.error("Error loading GIF for static thumbnail:", err);
      setGifStaticThumbnail("");
      setIsLoading(false);
      setHasError(true);
    };

    // 清理函数
    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [element.type, element.properties?.src]);

  return {
    gifStaticThumbnail,
    isLoading,
    hasError,
  };
};

/**
 * 处理视频缩略图的Hook
 * @param element 视频元素
 * @returns 包含缩略图URL和加载状态的对象
 */
export const useVideoThumbnail = (element: any) => {
  const [videoThumbnail, setVideoThumbnail] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [loadingProgress, setLoadingProgress] = React.useState(0);
  const [hasError, setHasError] = React.useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  useEffect(() => {
    if (element.type !== "video" || !element.properties?.src) {
      setVideoThumbnail("");
      setIsLoading(false);
      setLoadingProgress(0);
      setHasError(false);
      return;
    }

    // 重置状态
    setVideoThumbnail("");
    setIsLoading(true);
    setLoadingProgress(0);
    setHasError(false);

    const video = document.createElement("video");
    video.crossOrigin = "anonymous";
    video.src = element.properties.src;

    // 监听加载进度
    video.onprogress = () => {
      if (video.buffered.length > 0) {
        const bufferedEnd = video.buffered.end(video.buffered.length - 1);
        const duration = video.duration;
        if (duration > 0) {
          const progress = Math.min((bufferedEnd / duration) * 100, 100);
          setLoadingProgress(progress);
        }
      }
    };

    video.onloadstart = () => {
      setLoadingProgress(10); // 开始加载
    };

    video.onloadedmetadata = () => {
      setLoadingProgress(50); // 元数据加载完成
      video.currentTime = 0;
    };

    video.onseeked = () => {
      setLoadingProgress(80); // 定位完成
      const canvas = document.createElement("canvas");
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        setIsLoading(false);
        setHasError(true);
        return;
      }

      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      setVideoThumbnail(canvas.toDataURL("image/jpeg"));
      setLoadingProgress(100); // 缩略图生成完成
      setIsLoading(false);
    };

    video.onerror = (err) => {
      console.error("Error loading video for thumbnail:", err);
      setIsLoading(false);
      setHasError(true);
      setLoadingProgress(0);
    };

    videoRef.current = video;

    return () => {
      if (videoRef.current) {
        videoRef.current.src = "";
        videoRef.current = null;
      }
    };
  }, [element.type, element.properties?.src]);

  return {
    videoThumbnail,
    isLoading,
    loadingProgress,
    hasError,
  };
};

/**
 * 处理音频波形的Hook
 * @param element 音频元素
 * @returns 音频波形图URL
 */
export const useAudioWaveform = (element: any) => {
  const [audioWaveform, setAudioWaveform] = React.useState("");
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const store = React.useContext(StoreContext);
  const waveformCacheRef = useRef(new Map<string, string>());

  // 波形图生成函数（带防抖）
  const generateWaveformDebounced = useCallback(
    debounce(
      (
        audioData: AudioBuffer,
        duration: number,
        timeFrame: any,
        zoomFactor: number,
        onComplete: (dataUrl: string) => void
      ) => {
        generateWaveform(
          audioData,
          duration,
          timeFrame,
          zoomFactor,
          onComplete
        );
      },
      150
    ),
    []
  );

  // 波形图生成核心函数
  const generateWaveform = useCallback(
    (
      audioBuffer: AudioBuffer,
      audioDuration: number,
      timeFrame: any,
      zoomFactor: number,
      onComplete: (dataUrl: string) => void
    ) => {
      // 计算缓存键
      const cacheKey = `${element.properties?.src}-${timeFrame.start}-${
        timeFrame.end
      }-${Math.floor(zoomFactor)}`;

      // 检查缓存
      if (waveformCacheRef.current.has(cacheKey)) {
        const cachedData = waveformCacheRef.current.get(cacheKey);
        if (cachedData) {
          onComplete(cachedData);
          return;
        }
      }

      // 获取音频数据
      const channelData = audioBuffer.getChannelData(0);
      const elementDuration = timeFrame.end - timeFrame.start;
      const startTime = performance.now();

      try {
        renderWaveform(
          channelData,
          audioDuration,
          elementDuration,
          zoomFactor,
          (dataUrl) => {
            // 更新缓存
            if (waveformCacheRef.current.size > 20) {
              // 限制缓存大小
              const oldestKey = waveformCacheRef.current.keys().next().value;
              waveformCacheRef.current.delete(oldestKey);
            }
            waveformCacheRef.current.set(cacheKey, dataUrl);
            onComplete(dataUrl);

            // 性能日志
            const endTime = performance.now();
            if (endTime - startTime > 100) {
              console.debug(
                `Waveform rendering took ${Math.round(endTime - startTime)}ms`
              );
            }
          }
        );
      } catch (err) {
        console.error("Error generating audio waveform:", err);
        onComplete("");
      }
    },
    [element.properties?.src]
  );

  // 负责实际渲染波形的函数
  const renderWaveform = (
    channelData: Float32Array,
    audioDuration: number,
    elementDuration: number,
    zoomFactor: number,
    callback: (dataUrl: string) => void
  ) => {
    // 基础参数计算
    const baseWidth = 500;
    const pixelRatio = Math.min(window.devicePixelRatio || 1, 2);
    const zoomScale = Math.min(Math.max(1, 10 / zoomFactor), 3);
    const resolutionMultiplier = pixelRatio * Math.min(2, zoomScale);
    const canvasWidth = Math.floor(baseWidth * resolutionMultiplier);
    const canvasHeight = Math.floor(28 * pixelRatio);

    // 创建canvas
    const canvas = document.createElement("canvas");
    canvas.width = canvasWidth;
    canvas.height = canvasHeight;
    const ctx = canvas.getContext("2d", { alpha: true });
    if (!ctx) return;

    ctx.imageSmoothingEnabled = false;
    ctx.scale(resolutionMultiplier, pixelRatio);

    // 绘制背景
    const bgGradient = ctx.createLinearGradient(0, 0, 0, 28);
    bgGradient.addColorStop(0, "rgba(63, 81, 181, 0.9)");
    bgGradient.addColorStop(1, "rgba(63, 81, 181, 0.6)");
    ctx.fillStyle = bgGradient;
    ctx.fillRect(0, 0, baseWidth, 28);

    // 绘制网格线
    if (zoomFactor > 5) {
      drawWaveformGrid(ctx, baseWidth, elementDuration, pixelRatio);
    }

    // 计算采样参数
    const samplingFactor = Math.max(1, Math.floor(4 / zoomScale));
    const pointsPerPixel = Math.max(
      1,
      Math.floor(resolutionMultiplier / samplingFactor)
    );
    const pointCount = Math.min(canvasWidth, baseWidth * pointsPerPixel);

    // 计算最大振幅
    const maxAmplitude = calculateMaxAmplitude(channelData);
    const normalizeAmplitude = (value: number) =>
      Math.min(0.95, value / maxAmplitude);

    // 绘制波形
    const centerY = 28 / 2;
    const amplitudeScale = 28 * 0.38;
    const batchSize = Math.min(500, pointCount);

    // 绘制波形
    drawWaveformElements(
      ctx,
      channelData,
      pointCount,
      baseWidth,
      centerY,
      amplitudeScale,
      normalizeAmplitude,
      batchSize,
      zoomFactor
    );

    // 添加时间标记
    if (elementDuration > 1 && zoomFactor > 2) {
      drawTimeMarkers(ctx, baseWidth, elementDuration, centerY, pixelRatio);
    }

    // 高光点效果
    if (zoomFactor < 8) {
      drawHighlightPoints(ctx, baseWidth, pixelRatio);
    }

    // 输出图像
    const imageQuality = Math.min(
      0.95,
      0.8 + 0.15 * (1 / Math.min(zoomFactor, 10))
    );
    callback(canvas.toDataURL("image/png", imageQuality));
  };

  // 绘制波形网格
  const drawWaveformGrid = (
    ctx: CanvasRenderingContext2D,
    baseWidth: number,
    elementDuration: number,
    pixelRatio: number
  ) => {
    ctx.strokeStyle = "rgba(255, 255, 255, 0.05)";
    ctx.lineWidth = 0.5 / pixelRatio;

    // 水平线
    for (let y = 4; y < 28; y += 8) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(baseWidth, y);
      ctx.stroke();
    }

    // 垂直线
    const gridCount = Math.min(10, Math.ceil(elementDuration));
    const gridSpacing = baseWidth / gridCount;
    for (let x = 0; x < baseWidth; x += gridSpacing) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, 28);
      ctx.stroke();
    }
  };

  // 计算最大振幅
  const calculateMaxAmplitude = (channelData: Float32Array) => {
    let maxAmplitude = 0;
    const sampleCount = Math.min(10000, channelData.length);
    const skipFactor = Math.max(
      1,
      Math.floor(channelData.length / sampleCount)
    );

    for (let i = 0; i < channelData.length; i += skipFactor) {
      const amplitude = Math.abs(channelData[i]);
      maxAmplitude = Math.max(maxAmplitude, amplitude);
    }

    return Math.max(maxAmplitude, 0.05);
  };

  // 绘制波形元素
  const drawWaveformElements = (
    ctx: CanvasRenderingContext2D,
    channelData: Float32Array,
    pointCount: number,
    baseWidth: number,
    centerY: number,
    amplitudeScale: number,
    normalizeAmplitude: (value: number) => number,
    batchSize: number,
    zoomFactor: number
  ) => {
    // 优化的绘制函数
    const drawOptimizedWaveform = (
      renderCallback: (
        x: number,
        normalizedRms: number,
        isFirst: boolean
      ) => void
    ) => {
      const samplesPerPoint = Math.max(
        1,
        Math.floor(channelData.length / pointCount)
      );

      // 分批处理
      const processBatch = (startIndex: number, endIndex: number) => {
        ctx.beginPath();
        let isFirst = true;

        for (let i = startIndex; i < endIndex; i++) {
          // 计算音频位置
          const audioPosition = (i / pointCount) * channelData.length;
          const dataIndex = Math.floor(audioPosition);

          // 计算振幅
          let sum = 0;
          let count = 0;
          const sampleLimit = Math.min(samplesPerPoint, 20);

          for (
            let j = 0;
            j < sampleLimit && dataIndex + j < channelData.length;
            j++
          ) {
            sum += channelData[dataIndex + j] * channelData[dataIndex + j];
            count++;
          }

          const rms = Math.sqrt(sum / count);
          const normalizedRms = normalizeAmplitude(rms);

          // 计算坐标
          const x = (i / pointCount) * baseWidth;
          renderCallback(x, normalizedRms, isFirst);
          isFirst = false;
        }

        ctx.stroke();
      };

      // 处理所有批次
      for (let batch = 0; batch < pointCount; batch += batchSize) {
        const end = Math.min(batch + batchSize, pointCount);
        processBatch(batch, end);
      }
    };

    const pixelRatio = Math.min(window.devicePixelRatio || 1, 2);

    // 绘制波形阴影
    ctx.lineWidth = 1 / pixelRatio;
    ctx.strokeStyle = "rgba(0, 0, 0, 0.15)";
    drawOptimizedWaveform((x, normalizedRms, isFirst) => {
      const y = centerY + normalizedRms * amplitudeScale;
      if (isFirst) ctx.moveTo(x, y);
      else ctx.lineTo(x, y);
    });

    // 绘制波形顶部
    ctx.lineWidth = 1.5 / pixelRatio;
    const waveGradientTop = ctx.createLinearGradient(
      0,
      centerY - amplitudeScale,
      0,
      centerY
    );
    waveGradientTop.addColorStop(0, "rgba(255, 255, 255, 0.9)");
    waveGradientTop.addColorStop(1, "rgba(132, 189, 255, 0.8)");
    ctx.strokeStyle = waveGradientTop;

    drawOptimizedWaveform((x, normalizedRms, isFirst) => {
      const y = centerY - normalizedRms * amplitudeScale;
      if (isFirst) ctx.moveTo(x, y);
      else ctx.lineTo(x, y);
    });

    // 绘制波形底部
    const waveGradientBottom = ctx.createLinearGradient(
      0,
      centerY,
      0,
      centerY + amplitudeScale
    );
    waveGradientBottom.addColorStop(0, "rgba(132, 189, 255, 0.8)");
    waveGradientBottom.addColorStop(1, "rgba(255, 255, 255, 0.9)");
    ctx.strokeStyle = waveGradientBottom;

    drawOptimizedWaveform((x, normalizedRms, isFirst) => {
      const y = centerY + normalizedRms * amplitudeScale;
      if (isFirst) ctx.moveTo(x, y);
      else ctx.lineTo(x, y);
    });

    // 填充波形
    if (zoomFactor < 15) {
      drawWaveformFill(
        ctx,
        centerY,
        amplitudeScale,
        drawOptimizedWaveform,
        pixelRatio
      );
    }
  };

  // 绘制波形填充
  const drawWaveformFill = (
    ctx: CanvasRenderingContext2D,
    centerY: number,
    amplitudeScale: number,
    drawOptimizedWaveform: (
      renderCallback: (
        x: number,
        normalizedRms: number,
        isFirst: boolean
      ) => void
    ) => void,
    pixelRatio: number
  ) => {
    ctx.beginPath();
    const points: { x: number; y: number }[] = [];
    let isFirst = true;

    drawOptimizedWaveform((x, normalizedRms, isFirstPoint) => {
      const y = centerY - normalizedRms * amplitudeScale * 0.7;
      points.push({ x, y });

      if (isFirstPoint) {
        ctx.moveTo(x, y);
        isFirst = false;
      } else {
        ctx.lineTo(x, y);
      }
    });

    // 反向绘制下半部分
    for (let i = points.length - 1; i >= 0; i--) {
      const { x } = points[i];
      const normalizedRms =
        points[i].y > centerY
          ? (points[i].y - centerY) / (amplitudeScale * 0.7)
          : (centerY - points[i].y) / (amplitudeScale * 0.7);
      const y = centerY + normalizedRms * amplitudeScale * 0.7;
      ctx.lineTo(x, y);
    }

    // 填充渐变
    const fillGradient = ctx.createLinearGradient(0, 0, 0, 28);
    fillGradient.addColorStop(0, "rgba(255, 255, 255, 0.05)");
    fillGradient.addColorStop(0.5, "rgba(255, 255, 255, 0.2)");
    fillGradient.addColorStop(1, "rgba(255, 255, 255, 0.05)");
    ctx.fillStyle = fillGradient;
    ctx.fill();

    // 中心线
    ctx.beginPath();
    ctx.strokeStyle = "rgba(255, 255, 255, 0.4)";
    ctx.lineWidth = 0.5 / pixelRatio;
    ctx.setLineDash([2 / pixelRatio, 2 / pixelRatio]);
    ctx.moveTo(0, centerY);
    ctx.lineTo(500, centerY); // 使用固定值替代baseWidth
    ctx.stroke();
    ctx.setLineDash([]);
  };

  // 绘制时间标记
  const drawTimeMarkers = (
    ctx: CanvasRenderingContext2D,
    baseWidth: number,
    elementDuration: number,
    centerY: number,
    pixelRatio: number
  ) => {
    const interval = Math.max(
      1,
      Math.ceil(elementDuration / Math.min(5, elementDuration))
    );

    ctx.font = `${7 / pixelRatio}px Arial`;
    ctx.fillStyle = "rgba(255, 255, 255, 0.7)";
    ctx.textAlign = "center";

    for (let t = 0; t <= elementDuration; t += interval) {
      const x = (t / elementDuration) * baseWidth;

      // 刻度线
      ctx.beginPath();
      ctx.strokeStyle = "rgba(255, 255, 255, 0.4)";
      ctx.lineWidth = 0.5 / pixelRatio;
      ctx.moveTo(x, centerY - 4);
      ctx.lineTo(x, centerY + 4);
      ctx.stroke();

      // 时间文本
      if (x > 15 && x < baseWidth - 15 && interval >= 1) {
        ctx.fillText(`${t}s`, x, 28 - 2);
      }
    }
  };

  // 绘制高光点
  const drawHighlightPoints = (
    ctx: CanvasRenderingContext2D,
    baseWidth: number,
    pixelRatio: number
  ) => {
    ctx.fillStyle = "rgba(255, 255, 255, 0.5)";
    const highlightCount = Math.min(8, Math.floor(baseWidth / 80));

    for (let i = 0; i < highlightCount; i++) {
      const x =
        (i / highlightCount) * baseWidth +
        Math.random() * (baseWidth / highlightCount);
      const y = Math.random() * 28;
      const size = (0.5 + Math.random() * 0.5) / pixelRatio;
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fill();
    }
  };

  useEffect(() => {
    if (element.type !== "audio" || !element.properties?.src) return;

    let isMounted = true;
    const cacheKey = `${element.properties.src}-${element.timeFrame.start}-${
      element.timeFrame.end
    }-${Math.floor(store.timelineDisplayDuration)}`;

    // 检查缓存
    if (waveformCacheRef.current.has(cacheKey) && isMounted) {
      setAudioWaveform(waveformCacheRef.current.get(cacheKey) || "");
      return;
    }

    const audio = new Audio();
    audio.crossOrigin = "anonymous";
    audio.src = element.properties.src;

    audio.onloadedmetadata = async () => {
      let audioContext: AudioContext | null = null;
      try {
        if (!isMounted) return;

        audioContext = new AudioContext();
        const response = await fetch(element.properties.src);
        const arrayBuffer = await response.arrayBuffer();
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

        generateWaveformDebounced(
          audioBuffer,
          audioBuffer.duration,
          element.timeFrame,
          store.timelineDisplayDuration,
          (dataUrl) => {
            if (isMounted) {
              setAudioWaveform(dataUrl);
            }
            // 关闭AudioContext以释放资源
            if (audioContext && audioContext.state !== "closed") {
              audioContext
                .close()
                .catch((err) =>
                  console.warn("Error closing waveform AudioContext:", err)
                );
            }
          }
        );
      } catch (err) {
        console.error("Error processing audio for waveform:", err);
        // 确保在错误情况下也关闭AudioContext
        if (audioContext && audioContext.state !== "closed") {
          audioContext
            .close()
            .catch((err) =>
              console.warn("Error closing AudioContext after error:", err)
            );
        }
      }
    };

    audio.onerror = (err) => {
      console.error("Error loading audio for waveform:", err);
      // 当音频加载失败时，设置一个默认波形或空状态
      if (isMounted) {
        // 将错误状态存入缓存，避免重复尝试加载失败的音频
        waveformCacheRef.current.set(cacheKey, "error");

        // 通知状态更新，可以在UI中显示加载失败状态
        setAudioWaveform("error");

        // 记录错误信息
        console.warn(
          `音频文件 [${element.properties?.src}] 加载失败，元素ID: ${element.id}`
        );
      }
    };

    audioRef.current = audio;

    return () => {
      isMounted = false;
      if (audioRef.current) {
        // 在组件卸载前取消所有未完成的加载
        audioRef.current.src = "";
        audioRef.current.onloadedmetadata = null;
        audioRef.current.onerror = null;
        audioRef.current = null;
      }
    };
  }, [
    element.type,
    element.properties?.src,
    element.timeFrame,
    store.timelineDisplayDuration,
    store.timelinePan.offsetX,
    generateWaveformDebounced,
  ]);

  // 在返回audioWaveform前，检查是否为错误状态并提供默认值
  return audioWaveform === "error"
    ? "" // 返回空字符串表示使用默认波形UI
    : audioWaveform;
};
