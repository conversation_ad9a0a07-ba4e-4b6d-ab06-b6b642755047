/**
 * Wavesurfer.js 管理器 - 用于渲染音频波形
 */

import WaveSurfer from 'wavesurfer.js';

interface WaveformConfig {
  container: HTMLElement;
  height: number;
  waveColor: string;
  progressColor: string;
  backgroundColor: string;
  barWidth: number;
  barGap: number;
  normalize: boolean;
  responsive: boolean;
}

interface WaveformData {
  peaks: Float32Array;
  duration: number;
}

export class WavesurferManager {
  private static instance: WavesurferManager;
  private wavesurfers: Map<string, WaveSurfer> = new Map();
  private waveformCache: Map<string, WaveformData> = new Map();

  static getInstance(): WavesurferManager {
    if (!WavesurferManager.instance) {
      WavesurferManager.instance = new WavesurferManager();
    }
    return WavesurferManager.instance;
  }

  /**
   * 创建波形实例
   * @param elementId 音频元素ID
   * @param config 波形配置
   * @returns WaveSurfer实例
   */
  createWaveform(elementId: string, config: Partial<WaveformConfig>): WaveSurfer {
    // 如果已存在，先销毁
    if (this.wavesurfers.has(elementId)) {
      this.destroyWaveform(elementId);
    }

    const defaultConfig: WaveformConfig = {
      container: config.container!,
      height: 60,
      waveColor: '#4A90E2',
      progressColor: '#2E5BBA',
      backgroundColor: 'transparent',
      barWidth: 2,
      barGap: 1,
      normalize: true,
      responsive: true,
    };

    const finalConfig = { ...defaultConfig, ...config };

    const wavesurfer = WaveSurfer.create({
      container: finalConfig.container,
      height: finalConfig.height,
      waveColor: finalConfig.waveColor,
      progressColor: finalConfig.progressColor,
      backgroundColor: finalConfig.backgroundColor,
      barWidth: finalConfig.barWidth,
      barGap: finalConfig.barGap,
      normalize: finalConfig.normalize,
      responsive: finalConfig.responsive,
      interact: false, // 禁用交互，因为我们只用于显示
      cursorWidth: 0, // 隐藏光标
    });

    this.wavesurfers.set(elementId, wavesurfer);
    return wavesurfer;
  }

  /**
   * 从音频URL加载波形
   * @param elementId 音频元素ID
   * @param audioUrl 音频URL
   * @param config 波形配置
   */
  async loadWaveformFromUrl(
    elementId: string,
    audioUrl: string,
    config: Partial<WaveformConfig>
  ): Promise<WaveSurfer> {
    const wavesurfer = this.createWaveform(elementId, config);

    try {
      await wavesurfer.load(audioUrl);
      return wavesurfer;
    } catch (error) {
      console.error('Failed to load waveform from URL:', error);
      this.destroyWaveform(elementId);
      throw error;
    }
  }

  /**
   * 从AudioBuffer生成波形
   * @param elementId 音频元素ID
   * @param audioBuffer AudioBuffer数据
   * @param config 波形配置
   */
  async loadWaveformFromBuffer(
    elementId: string,
    audioBuffer: AudioBuffer,
    config: Partial<WaveformConfig>
  ): Promise<WaveSurfer> {
    const wavesurfer = this.createWaveform(elementId, config);

    try {
      // 从AudioBuffer生成peaks数据
      const peaks = this.extractPeaksFromBuffer(audioBuffer, config.container!.clientWidth || 800);
      
      // 缓存波形数据
      this.waveformCache.set(elementId, {
        peaks,
        duration: audioBuffer.duration
      });

      // 加载peaks数据到wavesurfer
      wavesurfer.loadBlob(this.createWaveBlob(peaks, audioBuffer.sampleRate));
      
      return wavesurfer;
    } catch (error) {
      console.error('Failed to load waveform from buffer:', error);
      this.destroyWaveform(elementId);
      throw error;
    }
  }

  /**
   * 从AudioBuffer提取peaks数据
   * @param audioBuffer AudioBuffer
   * @param width 波形宽度（像素）
   * @returns Float32Array peaks数据
   */
  private extractPeaksFromBuffer(audioBuffer: AudioBuffer, width: number): Float32Array {
    const sampleSize = audioBuffer.length / width;
    const sampleStep = ~~(sampleSize / 10) || 1;
    const mergedPeaks = [];

    for (let c = 0; c < audioBuffer.numberOfChannels; c++) {
      const peaks = [];
      const chan = audioBuffer.getChannelData(c);

      for (let i = 0; i < width; i++) {
        const start = ~~(i * sampleSize);
        const end = ~~(start + sampleSize);
        let min = 0;
        let max = 0;

        for (let j = start; j < end; j += sampleStep) {
          const value = chan[j];
          if (value > max) {
            max = value;
          }
          if (value < min) {
            min = value;
          }
        }

        peaks[2 * i] = max;
        peaks[2 * i + 1] = min;
      }

      mergedPeaks.push(peaks);
    }

    // 合并多声道数据
    const peaks = mergedPeaks[0];
    for (let c = 1; c < mergedPeaks.length; c++) {
      for (let i = 0; i < peaks.length; i++) {
        peaks[i] += mergedPeaks[c][i];
      }
    }

    // 归一化
    const max = Math.max(...peaks.map(Math.abs));
    if (max > 0) {
      for (let i = 0; i < peaks.length; i++) {
        peaks[i] /= max;
      }
    }

    return new Float32Array(peaks);
  }

  /**
   * 创建波形Blob数据
   * @param peaks peaks数据
   * @param sampleRate 采样率
   * @returns Blob
   */
  private createWaveBlob(peaks: Float32Array, sampleRate: number): Blob {
    const length = peaks.length / 2;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);

    // WAV文件头
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);

    // 写入音频数据
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, peaks[i * 2]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }

    return new Blob([arrayBuffer], { type: 'audio/wav' });
  }

  /**
   * 更新波形进度
   * @param elementId 音频元素ID
   * @param progress 进度 (0-1)
   */
  updateProgress(elementId: string, progress: number): void {
    const wavesurfer = this.wavesurfers.get(elementId);
    if (wavesurfer) {
      wavesurfer.seekTo(progress);
    }
  }

  /**
   * 设置波形颜色
   * @param elementId 音频元素ID
   * @param waveColor 波形颜色
   * @param progressColor 进度颜色
   */
  setColors(elementId: string, waveColor: string, progressColor: string): void {
    const wavesurfer = this.wavesurfers.get(elementId);
    if (wavesurfer) {
      wavesurfer.setOptions({
        waveColor,
        progressColor
      });
    }
  }

  /**
   * 销毁波形实例
   * @param elementId 音频元素ID
   */
  destroyWaveform(elementId: string): void {
    const wavesurfer = this.wavesurfers.get(elementId);
    if (wavesurfer) {
      wavesurfer.destroy();
      this.wavesurfers.delete(elementId);
    }
    this.waveformCache.delete(elementId);
  }

  /**
   * 销毁所有波形实例
   */
  destroyAll(): void {
    for (const [elementId] of this.wavesurfers) {
      this.destroyWaveform(elementId);
    }
  }

  /**
   * 获取波形实例
   * @param elementId 音频元素ID
   * @returns WaveSurfer实例或undefined
   */
  getWaveform(elementId: string): WaveSurfer | undefined {
    return this.wavesurfers.get(elementId);
  }

  /**
   * 获取缓存的波形数据
   * @param elementId 音频元素ID
   * @returns 波形数据或undefined
   */
  getCachedWaveformData(elementId: string): WaveformData | undefined {
    return this.waveformCache.get(elementId);
  }
}

// 导出单例实例
export const wavesurferManager = WavesurferManager.getInstance();
