/**
 * 音频性能监控工具
 */

interface AudioMetrics {
  totalAudioElements: number;
  activeConnections: number;
  memoryUsage: number;
  playbackErrors: number;
  lastError?: string;
}

class AudioMonitor {
  private static instance: AudioMonitor;
  private metrics: AudioMetrics = {
    totalAudioElements: 0,
    activeConnections: 0,
    memoryUsage: 0,
    playbackErrors: 0,
  };
  private errorLog: string[] = [];
  private maxErrorLogSize = 50;

  static getInstance(): AudioMonitor {
    if (!AudioMonitor.instance) {
      AudioMonitor.instance = new AudioMonitor();
    }
    return AudioMonitor.instance;
  }

  /**
   * 记录音频错误
   */
  logError(error: string, context?: string): void {
    const timestamp = new Date().toISOString();
    const errorMessage = `[${timestamp}] ${context ? `${context}: ` : ''}${error}`;
    
    this.errorLog.push(errorMessage);
    if (this.errorLog.length > this.maxErrorLogSize) {
      this.errorLog.shift();
    }
    
    this.metrics.playbackErrors++;
    this.metrics.lastError = errorMessage;
    
    console.warn('Audio Error:', errorMessage);
  }

  /**
   * 更新音频指标
   */
  updateMetrics(metrics: Partial<AudioMetrics>): void {
    Object.assign(this.metrics, metrics);
  }

  /**
   * 获取当前指标
   */
  getMetrics(): AudioMetrics {
    return { ...this.metrics };
  }

  /**
   * 获取错误日志
   */
  getErrorLog(): string[] {
    return [...this.errorLog];
  }

  /**
   * 清除错误日志
   */
  clearErrorLog(): void {
    this.errorLog = [];
    this.metrics.playbackErrors = 0;
    delete this.metrics.lastError;
  }

  /**
   * 检查音频健康状态
   */
  checkAudioHealth(): {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
  } {
    const issues: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    // 检查错误率
    if (this.metrics.playbackErrors > 10) {
      issues.push(`高错误率: ${this.metrics.playbackErrors} 个错误`);
      status = 'warning';
    }

    // 检查音频元素数量
    if (this.metrics.totalAudioElements > 20) {
      issues.push(`音频元素过多: ${this.metrics.totalAudioElements}`);
      status = 'warning';
    }

    // 检查活跃连接数
    if (this.metrics.activeConnections > 10) {
      issues.push(`活跃连接过多: ${this.metrics.activeConnections}`);
      status = 'critical';
    }

    return { status, issues };
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const health = this.checkAudioHealth();
    const report = [
      '=== 音频性能报告 ===',
      `状态: ${health.status}`,
      `音频元素总数: ${this.metrics.totalAudioElements}`,
      `活跃连接数: ${this.metrics.activeConnections}`,
      `播放错误数: ${this.metrics.playbackErrors}`,
      `内存使用: ${this.metrics.memoryUsage.toFixed(2)} MB`,
      '',
      '问题:',
      ...health.issues.map(issue => `- ${issue}`),
      '',
      '最近错误:',
      ...this.errorLog.slice(-5).map(error => `- ${error}`)
    ];

    return report.join('\n');
  }
}

export const audioMonitor = AudioMonitor.getInstance();

// 在开发环境中添加到全局
if (process.env.NODE_ENV === 'development') {
  (window as any).audioMonitor = audioMonitor;
}
