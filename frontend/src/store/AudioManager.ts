/**
 * 音频管理器 - 统一管理音频上下文和播放状态
 * 解决音频爆破音和删除后仍有声音的问题
 */

interface AudioContextData {
  audioContext: AudioContext;
  sourceNode: MediaElementAudioSourceNode;
  destination: MediaStreamAudioDestinationNode;
  elementId: string;
}

export class AudioManager {
  private static instance: AudioManager;
  private audioContextMap: Map<string, AudioContextData> = new Map();
  private globalAudioContext: AudioContext | null = null;
  private audioCache: Map<string, HTMLAudioElement> = new Map();
  private maxCacheSize: number = 10;

  private constructor() {}

  static getInstance(): AudioManager {
    if (!AudioManager.instance) {
      AudioManager.instance = new AudioManager();
    }
    return AudioManager.instance;
  }

  /**
   * 获取或创建全局AudioContext
   */
  private getGlobalAudioContext(): AudioContext {
    if (
      !this.globalAudioContext ||
      this.globalAudioContext.state === "closed"
    ) {
      this.globalAudioContext = new AudioContext();
    }
    return this.globalAudioContext;
  }

  /**
   * 为音频元素创建AudioContext连接
   * @param audioElement HTML音频元素
   * @param elementId 元素ID
   * @returns MediaStream用于录制
   */
  createAudioConnection(
    audioElement: HTMLAudioElement,
    elementId: string
  ): MediaStream | null {
    try {
      // 检查是否已经存在连接
      if (this.audioContextMap.has(elementId)) {
        const existing = this.audioContextMap.get(elementId)!;
        return existing.destination.stream;
      }

      const audioContext = this.getGlobalAudioContext();
      const sourceNode = audioContext.createMediaElementSource(audioElement);
      const destination = audioContext.createMediaStreamDestination();

      // 只连接到destination，避免反馈
      sourceNode.connect(destination);

      const contextData: AudioContextData = {
        audioContext,
        sourceNode,
        destination,
        elementId,
      };

      this.audioContextMap.set(elementId, contextData);

      // 存储到音频元素上，便于清理
      (audioElement as any)._audioContextData = contextData;

      console.log(`Created audio connection for element: ${elementId}`);
      return destination.stream;
    } catch (error) {
      console.error("Error creating audio connection:", error);
      return null;
    }
  }

  /**
   * 清理指定音频元素的连接
   * @param elementId 元素ID
   */
  cleanupAudioConnection(elementId: string): void {
    const contextData = this.audioContextMap.get(elementId);
    if (contextData) {
      try {
        // 断开连接
        contextData.sourceNode.disconnect();

        // 从映射中移除
        this.audioContextMap.delete(elementId);

        console.log(`Cleaned up audio connection for element: ${elementId}`);
      } catch (error) {
        console.warn("Error during audio connection cleanup:", error);
      }
    }
  }

  /**
   * 清理所有音频连接
   */
  cleanupAllConnections(): void {
    for (const [elementId, contextData] of this.audioContextMap.entries()) {
      try {
        contextData.sourceNode.disconnect();
      } catch (error) {
        console.warn(`Error cleaning up connection for ${elementId}:`, error);
      }
    }
    this.audioContextMap.clear();

    // 关闭全局AudioContext
    if (this.globalAudioContext && this.globalAudioContext.state !== "closed") {
      this.globalAudioContext
        .close()
        .catch((err) =>
          console.warn("Error closing global AudioContext:", err)
        );
      this.globalAudioContext = null;
    }

    // 清理音频缓存
    this.clearAudioCache();

    console.log("All audio connections and cache cleaned up");
  }

  /**
   * 暂停所有音频播放
   */
  pauseAllAudio(): void {
    document.querySelectorAll("audio").forEach((audio) => {
      if (!audio.paused) {
        audio.pause();
      }
    });
  }

  /**
   * 设置音频音量，避免爆破音
   * @param audioElement 音频元素
   * @param volume 音量值 (0-1)
   */
  setAudioVolume(audioElement: HTMLAudioElement, volume: number = 0.8): void {
    audioElement.volume = Math.min(Math.max(volume, 0), 1);
  }

  /**
   * 预加载音频
   * @param src 音频源URL
   * @returns Promise<HTMLAudioElement>
   */
  async preloadAudio(src: string): Promise<HTMLAudioElement> {
    const cached = this.getCachedAudioElement(src);
    if (cached) {
      return cached;
    }

    return new Promise((resolve, reject) => {
      const audio = new Audio();
      audio.preload = "metadata";
      audio.crossOrigin = "anonymous";

      audio.onloadedmetadata = () => {
        this.cacheAudioElement(src, audio);
        resolve(audio);
      };

      audio.onerror = () => {
        reject(new Error(`Failed to preload audio: ${src}`));
      };

      audio.src = src;
    });
  }

  /**
   * 安全播放音频
   * @param audioElement 音频元素
   * @param startTime 开始时间（秒）
   * @param playbackRate 播放速度（可选）
   */
  async safePlayAudio(
    audioElement: HTMLAudioElement,
    startTime: number = 0,
    playbackRate: number = 1
  ): Promise<void> {
    try {
      // 设置安全音量，避免爆破音
      this.setAudioVolume(audioElement, 0.7); // 降低默认音量

      // 设置播放速度
      audioElement.playbackRate = playbackRate;

      // 设置播放位置
      audioElement.currentTime = Math.max(0, startTime);

      // 播放音频
      await audioElement.play();
    } catch (error) {
      console.error("Error playing audio safely:", error);
    }
  }

  /**
   * 安全停止音频
   * @param audioElement 音频元素
   * @param resetTime 是否重置播放时间
   */
  safeStopAudio(
    audioElement: HTMLAudioElement,
    resetTime: boolean = true
  ): void {
    try {
      audioElement.pause();
      if (resetTime) {
        audioElement.currentTime = 0;
      }
    } catch (error) {
      console.error("Error stopping audio safely:", error);
    }
  }

  /**
   * 获取当前活跃的音频连接数量
   */
  getActiveConnectionCount(): number {
    return this.audioContextMap.size;
  }

  /**
   * 检查AudioContext状态
   */
  getAudioContextState(): string {
    return this.globalAudioContext?.state || "none";
  }

  /**
   * 缓存音频元素以提高性能
   * @param src 音频源URL
   * @param audioElement 音频元素
   */
  cacheAudioElement(src: string, audioElement: HTMLAudioElement): void {
    if (this.audioCache.size >= this.maxCacheSize) {
      // 移除最旧的缓存项
      const firstKey = this.audioCache.keys().next().value;
      this.audioCache.delete(firstKey);
    }
    this.audioCache.set(src, audioElement);
  }

  /**
   * 从缓存获取音频元素
   * @param src 音频源URL
   * @returns 缓存的音频元素或null
   */
  getCachedAudioElement(src: string): HTMLAudioElement | null {
    return this.audioCache.get(src) || null;
  }

  /**
   * 清理音频缓存
   */
  clearAudioCache(): void {
    this.audioCache.clear();
  }
}

// 导出单例实例
export const audioManager = AudioManager.getInstance();
