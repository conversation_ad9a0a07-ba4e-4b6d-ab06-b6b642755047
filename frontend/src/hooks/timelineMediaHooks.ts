import React, { useCallback, useEffect, useRef } from "react";
import { StoreContext } from "../store";
import { wavesurferManager } from "../utils/wavesurferManager";

/**
 * 函数防抖实现
 */
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout;
  return function (...args: any[]) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}

/**
 * 处理GIF静态缩略图的Hook
 * @param element GIF元素
 * @returns 包含缩略图URL和加载状态的对象
 */
export const useGifStaticThumbnail = (element: any) => {
  const [gifStaticThumbnail, setGifStaticThumbnail] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);

  useEffect(() => {
    if (element.type !== "gif" || !element.properties?.src) {
      setGifStaticThumbnail("");
      setIsLoading(false);
      setHasError(false);
      return;
    }

    // 重置状态
    setGifStaticThumbnail("");
    setIsLoading(true);
    setHasError(false);

    // 创建一个临时的img元素来加载GIF
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.src = element.properties.src;

    img.onload = () => {
      // 创建canvas来绘制GIF的第一帧
      const canvas = document.createElement("canvas");
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        setIsLoading(false);
        setHasError(true);
        return;
      }

      // 绘制图片（这会自动显示第一帧）
      ctx.drawImage(img, 0, 0);

      // 转换为静态图片URL
      setGifStaticThumbnail(canvas.toDataURL("image/jpeg", 0.8));
      setIsLoading(false);
    };

    img.onerror = (err) => {
      console.error("Error loading GIF for static thumbnail:", err);
      setGifStaticThumbnail("");
      setIsLoading(false);
      setHasError(true);
    };

    // 清理函数
    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [element.type, element.properties?.src]);

  return {
    gifStaticThumbnail,
    isLoading,
    hasError,
  };
};

/**
 * 处理视频缩略图的Hook
 * @param element 视频元素
 * @returns 包含缩略图URL和加载状态的对象
 */
export const useVideoThumbnail = (element: any) => {
  const [videoThumbnail, setVideoThumbnail] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);
  const [loadingProgress, setLoadingProgress] = React.useState(0);
  const [hasError, setHasError] = React.useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  useEffect(() => {
    if (element.type !== "video" || !element.properties?.src) {
      setVideoThumbnail("");
      setIsLoading(false);
      setLoadingProgress(0);
      setHasError(false);
      return;
    }

    // 重置状态
    setVideoThumbnail("");
    setIsLoading(true);
    setLoadingProgress(0);
    setHasError(false);

    const video = document.createElement("video");
    video.crossOrigin = "anonymous";
    video.src = element.properties.src;

    // 监听加载进度
    video.onprogress = () => {
      if (video.buffered.length > 0) {
        const bufferedEnd = video.buffered.end(video.buffered.length - 1);
        const duration = video.duration;
        if (duration > 0) {
          const progress = Math.min((bufferedEnd / duration) * 100, 100);
          setLoadingProgress(progress);
        }
      }
    };

    video.onloadstart = () => {
      setLoadingProgress(10); // 开始加载
    };

    video.onloadedmetadata = () => {
      setLoadingProgress(50); // 元数据加载完成
      video.currentTime = 0;
    };

    video.onseeked = () => {
      setLoadingProgress(80); // 定位完成
      const canvas = document.createElement("canvas");
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        setIsLoading(false);
        setHasError(true);
        return;
      }

      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      setVideoThumbnail(canvas.toDataURL("image/jpeg"));
      setLoadingProgress(100); // 缩略图生成完成
      setIsLoading(false);
    };

    video.onerror = (err) => {
      console.error("Error loading video for thumbnail:", err);
      setIsLoading(false);
      setHasError(true);
      setLoadingProgress(0);
    };

    videoRef.current = video;

    return () => {
      if (videoRef.current) {
        videoRef.current.src = "";
        videoRef.current = null;
      }
    };
  }, [element.type, element.properties?.src]);

  return {
    videoThumbnail,
    isLoading,
    loadingProgress,
    hasError,
  };
};

/**
 * 处理音频波形的Hook - 使用 Wavesurfer.js
 * @param element 音频元素
 * @returns 包含波形容器引用和状态的对象
 */
export const useAudioWaveform = (element: any) => {
  const [isWaveformReady, setIsWaveformReady] = React.useState(false);
  const [waveformError, setWaveformError] = React.useState<string | null>(null);
  const waveformContainerRef = useRef<HTMLDivElement | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const store = React.useContext(StoreContext);
  const isMountedRef = useRef(true);

  // 使用 Wavesurfer.js 生成波形的防抖函数
  const generateWaveformDebounced = useCallback(
    debounce(
      async (audioBuffer: AudioBuffer, elementId: string) => {
        if (!waveformContainerRef.current || !isMountedRef.current) return;

        try {
          setWaveformError(null);

          // 计算波形配置
          const containerWidth =
            waveformContainerRef.current.clientWidth || 800;
          const waveformConfig = {
            container: waveformContainerRef.current,
            height: 60,
            waveColor: "#4A90E2",
            progressColor: "#2E5BBA",
            backgroundColor: "transparent",
            barWidth: Math.max(1, Math.floor(containerWidth / 400)),
            barGap: 1,
            normalize: true,
            responsive: true,
          };

          // 使用 Wavesurfer.js 生成波形
          await wavesurferManager.loadWaveformFromBuffer(
            elementId,
            audioBuffer,
            waveformConfig
          );

          if (isMountedRef.current) {
            setIsWaveformReady(true);
          }
        } catch (error) {
          console.error(
            "Failed to generate waveform with Wavesurfer.js:",
            error
          );
          if (isMountedRef.current) {
            setWaveformError("Failed to generate waveform");
            setIsWaveformReady(false);
          }
        }
      },
      300 // 防抖延迟
    ),
    []
  );

  // 清理函数
  const cleanup = useCallback(() => {
    if (element.properties?.elementId) {
      wavesurferManager.destroyWaveform(element.properties.elementId);
    }
    isMountedRef.current = false;
  }, [element.properties?.elementId]);

  // 组件卸载时的清理
  React.useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // 主要的 useEffect - 处理音频波形生成
  useEffect(() => {
    if (element.type !== "audio" || !element.properties?.src) {
      setIsWaveformReady(false);
      setWaveformError(null);
      return;
    }

    if (!element.properties?.elementId) {
      setWaveformError("Missing element ID");
      return;
    }

    isMountedRef.current = true;
    setIsWaveformReady(false);
    setWaveformError(null);

    const loadAudioAndGenerateWaveform = async () => {
      let audioContext: AudioContext | null = null;

      try {
        // 创建音频上下文
        audioContext = new AudioContext();

        // 获取音频数据
        const response = await fetch(element.properties.src);
        const arrayBuffer = await response.arrayBuffer();
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

        // 使用防抖函数生成波形
        await generateWaveformDebounced(
          audioBuffer,
          element.properties.elementId
        );
      } catch (error) {
        console.error("Error loading audio for waveform:", error);
        if (isMountedRef.current) {
          setWaveformError("Failed to load audio");
          setIsWaveformReady(false);
        }
      } finally {
        // 清理 AudioContext
        if (audioContext && audioContext.state !== "closed") {
          audioContext
            .close()
            .catch((err) =>
              console.warn("Error closing waveform AudioContext:", err)
            );
        }
      }
    };

    loadAudioAndGenerateWaveform();

    // 清理函数
    return () => {
      isMountedRef.current = false;
    };
  }, [
    element.type,
    element.properties?.src,
    element.properties?.elementId,
    element.timeFrame,
    store.timelineDisplayDuration,
    generateWaveformDebounced,
  ]);

  // 返回波形容器引用和状态
  return {
    waveformContainerRef,
    isWaveformReady,
    waveformError,
  };
};
