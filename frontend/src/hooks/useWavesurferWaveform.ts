import React, { useRef, useEffect, useState, useCallback } from "react";
import WaveSurfer from "wavesurfer.js";
import { debounce } from "lodash";

interface WaveformOptions {
  width: number;
  height: number;
  waveColor: string;
  progressColor: string;
  backgroundColor: string;
  barWidth?: number;
  barGap?: number;
  normalize?: boolean;
}

interface TimelineOptions {
  height: number;
  timeInterval: number;
  primaryLabelInterval: number;
  secondaryLabelInterval: number;
  style: {
    fontSize: string;
    color: string;
  };
}

/**
 * 使用 Wavesurfer.js 生成音频波形的 Hook
 * @param audioSrc 音频源URL
 * @param timeFrame 时间帧信息
 * @param options 波形配置选项
 * @returns 波形相关的状态和方法
 */
export const useWavesurferWaveform = (
  audioSrc: string,
  timeFrame: { start: number; end: number },
  options: Partial<WaveformOptions> = {}
) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const wavesurferRef = useRef<WaveSurfer | null>(null);
  const [waveformDataUrl, setWaveformDataUrl] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [duration, setDuration] = useState(0);

  // 默认配置
  const defaultOptions: WaveformOptions = {
    width: 800,
    height: 60,
    waveColor: "#4A90E2",
    progressColor: "#2E5BBA",
    backgroundColor: "transparent",
    barWidth: 2,
    barGap: 1,
    normalize: true,
    ...options,
  };

  // 创建离屏 canvas 来生成波形图片
  const generateWaveformImage = useCallback(
    async (wavesurfer: WaveSurfer): Promise<string> => {
      return new Promise((resolve) => {
        // 等待波形渲染完成
        setTimeout(() => {
          try {
            const canvas = document.createElement("canvas");
            const ctx = canvas.getContext("2d");
            if (!ctx) {
              resolve("");
              return;
            }

            canvas.width = defaultOptions.width;
            canvas.height = defaultOptions.height;

            // 设置背景
            if (defaultOptions.backgroundColor !== "transparent") {
              ctx.fillStyle = defaultOptions.backgroundColor;
              ctx.fillRect(0, 0, canvas.width, canvas.height);
            }

            // 获取波形数据
            const peaks = wavesurfer.getDecodedData()?.getChannelData(0);
            if (!peaks) {
              resolve("");
              return;
            }

            // 计算时间范围内的数据
            const totalDuration = wavesurfer.getDuration();
            const startRatio = timeFrame.start / 1000 / totalDuration;
            const endRatio = timeFrame.end / 1000 / totalDuration;

            const startIndex = Math.floor(startRatio * peaks.length);
            const endIndex = Math.floor(endRatio * peaks.length);
            const segmentPeaks = peaks.slice(startIndex, endIndex);

            // 绘制波形
            const barWidth = defaultOptions.barWidth || 2;
            const barGap = defaultOptions.barGap || 1;
            const totalBarWidth = barWidth + barGap;
            const numBars = Math.floor(canvas.width / totalBarWidth);

            const samplesPerBar = Math.floor(segmentPeaks.length / numBars);

            ctx.fillStyle = defaultOptions.waveColor;

            for (let i = 0; i < numBars; i++) {
              const start = i * samplesPerBar;
              const end = start + samplesPerBar;

              // 计算这个区间的最大振幅
              let max = 0;
              for (let j = start; j < end && j < segmentPeaks.length; j++) {
                max = Math.max(max, Math.abs(segmentPeaks[j]));
              }

              // 归一化处理
              if (defaultOptions.normalize) {
                max = Math.min(max * 2, 1); // 增强可视性
              }

              const barHeight = max * canvas.height * 0.8; // 留出一些边距
              const x = i * totalBarWidth;
              const y = (canvas.height - barHeight) / 2;

              ctx.fillRect(x, y, barWidth, barHeight);
            }

            resolve(canvas.toDataURL());
          } catch (err) {
            console.error("Error generating waveform image:", err);
            resolve("");
          }
        }, 100);
      });
    },
    [defaultOptions, timeFrame]
  );

  // 防抖的波形生成函数
  const generateWaveformDebounced = useCallback(
    debounce(async (src: string) => {
      if (!src) return;

      setIsLoading(true);
      setError(null);

      let tempContainer: HTMLDivElement | null = null;

      try {
        // 清理之前的实例
        if (wavesurferRef.current) {
          wavesurferRef.current.destroy();
        }

        // 创建临时容器用于 Wavesurfer
        tempContainer = document.createElement("div");
        tempContainer.style.position = "absolute";
        tempContainer.style.left = "-9999px";
        tempContainer.style.top = "-9999px";
        tempContainer.style.visibility = "hidden";
        tempContainer.style.width = `${defaultOptions.width}px`;
        tempContainer.style.height = `${defaultOptions.height}px`;
        document.body.appendChild(tempContainer);

        // 创建新的 Wavesurfer 实例
        const wavesurfer = WaveSurfer.create({
          container: tempContainer,
          waveColor: defaultOptions.waveColor,
          progressColor: defaultOptions.progressColor,
          height: defaultOptions.height,
          normalize: defaultOptions.normalize,
          interact: false, // 禁用交互，只用于生成波形
        });

        wavesurferRef.current = wavesurfer;

        // 监听加载完成事件
        wavesurfer.on("ready", async () => {
          setDuration(wavesurfer.getDuration());

          // 生成波形图片
          const dataUrl = await generateWaveformImage(wavesurfer);
          setWaveformDataUrl(dataUrl);
          setIsLoading(false);

          // 清理临时容器
          setTimeout(() => {
            if (tempContainer && tempContainer.parentNode) {
              tempContainer.parentNode.removeChild(tempContainer);
            }
          }, 100);
        });

        // 监听错误事件
        wavesurfer.on("error", (err) => {
          console.error("Wavesurfer error:", err);
          setError("Failed to load audio waveform");
          setIsLoading(false);

          // 清理临时容器
          if (tempContainer && tempContainer.parentNode) {
            tempContainer.parentNode.removeChild(tempContainer);
          }
        });

        // 加载音频
        await wavesurfer.load(src);
      } catch (err) {
        console.error("Error creating wavesurfer:", err);
        setError("Failed to create waveform");
        setIsLoading(false);

        // 清理临时容器
        if (tempContainer && tempContainer.parentNode) {
          tempContainer.parentNode.removeChild(tempContainer);
        }
      }
    }, 300),
    [defaultOptions, generateWaveformImage]
  );

  // 当音频源或时间帧改变时重新生成波形
  useEffect(() => {
    if (audioSrc) {
      generateWaveformDebounced(audioSrc);
    }

    return () => {
      generateWaveformDebounced.cancel();
    };
  }, [audioSrc, timeFrame.start, timeFrame.end, generateWaveformDebounced]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (wavesurferRef.current) {
        wavesurferRef.current.destroy();
      }
    };
  }, []);

  return {
    containerRef,
    waveformDataUrl,
    isLoading,
    error,
    duration,
    wavesurfer: wavesurferRef.current,
  };
};
